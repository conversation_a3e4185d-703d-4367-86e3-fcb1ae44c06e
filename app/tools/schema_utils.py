"""
Utility functions for handling JSON schemas in tool loaders.
"""

import logging
from typing import Any, Dict

logger = logging.getLogger(__name__)


def resolve_schema_references(
    schema: Dict[str, Any], definitions: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Resolve $ref references in JSON schema using $defs definitions.

    Args:
        schema: The schema object that may contain $ref
        definitions: The $defs definitions from the root schema

    Returns:
        Resolved schema object
    """
    if definitions is None:
        definitions = {}

    if isinstance(schema, dict):
        if "$ref" in schema:
            ref_path = schema["$ref"]
            if ref_path.startswith("#/$defs/"):
                def_name = ref_path.replace("#/$defs/", "")
                if def_name in definitions:
                    return resolve_schema_references(definitions[def_name], definitions)
                else:
                    logger.warning(f"Definition {def_name} not found in $defs")
                    return schema
            else:
                logger.warning(f"Unsupported $ref format: {ref_path}")
                return schema
        else:
            # Recursively resolve references in nested objects
            resolved = {}
            for key, value in schema.items():
                resolved[key] = resolve_schema_references(value, definitions)
            return resolved
    elif isinstance(schema, list):
        return [resolve_schema_references(item, definitions) for item in schema]
    else:
        return schema


def simplify_schema_for_function_calling(
    schema: Dict[str, Any], definitions: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Simplify complex JSON schema to be compatible with function calling APIs like Vertex AI.
    
    This method converts complex schema constructs like anyOf, oneOf, etc. to simpler
    formats that function calling APIs can understand.
    
    Args:
        schema: The schema object to simplify
        definitions: Schema definitions for reference resolution
        
    Returns:
        Simplified schema object
    """
    if definitions is None:
        definitions = {}
        
    if not isinstance(schema, dict):
        return schema
        
    # Resolve any $ref first
    resolved_schema = resolve_schema_references(schema, definitions)
    simplified = {}
    
    # Copy basic properties
    for key in ["title", "description", "default"]:
        if key in resolved_schema:
            simplified[key] = resolved_schema[key]
    
    # Handle anyOf - convert to the first non-null type or string if complex
    if "anyOf" in resolved_schema:
        any_of_schemas = resolved_schema["anyOf"]
        non_null_schemas = [
            s for s in any_of_schemas if s.get("type") != "null"
        ]
        
        if non_null_schemas:
            # Use the first non-null schema as the base type
            first_schema = non_null_schemas[0]
            if "type" in first_schema:
                simplified["type"] = first_schema["type"]
                # Copy other relevant properties
                for prop in ["enum", "items", "properties", "required"]:
                    if prop in first_schema:
                        simplified[prop] = first_schema[prop]
            else:
                # Fallback to string if no type specified
                simplified["type"] = "string"
        else:
            # All schemas are null, make it optional string
            simplified["type"] = "string"
            
        # If there was a null type, copy the default
        has_null = any(s.get("type") == "null" for s in any_of_schemas)
        if has_null and "default" not in simplified:
            simplified["default"] = None
            
    # Handle oneOf similarly to anyOf
    elif "oneOf" in resolved_schema:
        one_of_schemas = resolved_schema["oneOf"]
        non_null_schemas = [
            s for s in one_of_schemas if s.get("type") != "null"
        ]
        
        if non_null_schemas:
            first_schema = non_null_schemas[0]
            if "type" in first_schema:
                simplified["type"] = first_schema["type"]
                for prop in ["enum", "items", "properties", "required"]:
                    if prop in first_schema:
                        simplified[prop] = first_schema[prop]
            else:
                simplified["type"] = "string"
        else:
            simplified["type"] = "string"
            
    # Handle allOf - merge all schemas (simplified approach)
    elif "allOf" in resolved_schema:
        all_of_schemas = resolved_schema["allOf"]
        for sub_schema in all_of_schemas:
            if "type" in sub_schema:
                simplified["type"] = sub_schema["type"]
            for prop in ["enum", "items", "properties", "required"]:
                if prop in sub_schema:
                    simplified[prop] = sub_schema[prop]
        
        # Default to string if no type found
        if "type" not in simplified:
            simplified["type"] = "string"
            
    # Handle regular schema
    else:
        # Copy the schema as-is, but recursively simplify nested objects
        for key, value in resolved_schema.items():
            if key == "properties" and isinstance(value, dict):
                # Recursively simplify properties
                simplified[key] = {
                    prop_name: simplify_schema_for_function_calling(
                        prop_schema, definitions
                    )
                    for prop_name, prop_schema in value.items()
                }
            elif key == "items" and isinstance(value, dict):
                # Recursively simplify array items
                simplified[key] = simplify_schema_for_function_calling(
                    value, definitions
                )
            else:
                simplified[key] = value
                
    # Ensure we have a type field
    if "type" not in simplified:
        simplified["type"] = "string"
        
    return simplified


def create_simplified_tool_schema_override(simplified_schema: Dict[str, Any]):
    """
    Create a schema property override method for BaseTool subclasses.
    
    This function returns a method that can be used to override the schema property
    of a BaseTool to return a simplified version compatible with function calling APIs.
    
    Args:
        simplified_schema: The simplified schema to use for function parameters
        
    Returns:
        A method that can be used as a schema property override
    """
    def schema_property(self) -> Dict[str, Any]:
        """
        Override the schema property to return a simplified version
        compatible with function calling APIs like Vertex AI.
        """
        # Get the base schema from the parent class
        base_schema = super(type(self), self).schema
        
        # Use our simplified schema for the parameters
        simplified = base_schema.copy()
        if "function" in simplified and "parameters" in simplified["function"]:
            simplified["function"]["parameters"] = simplified_schema
        
        return simplified
    
    return schema_property
